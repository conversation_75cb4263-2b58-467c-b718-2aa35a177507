<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

$favoritesFile = __DIR__ . '/favorites.json';

// Check if directory is writable
if (!is_writable(__DIR__)) {
    echo json_encode([
        'error' => 'Directory is not writable',
        'path' => __DIR__
    ]);
    exit;
}

// If file doesn't exist, create it
if (!file_exists($favoritesFile)) {
    $initialData = ['favorites' => []];
    if (file_put_contents($favoritesFile, json_encode($initialData, JSON_PRETTY_PRINT)) === false) {
        echo json_encode([
            'error' => 'Could not create favorites.json',
            'path' => $favoritesFile
        ]);
        exit;
    }
}

// Check if file is readable
if (!is_readable($favoritesFile)) {
    echo json_encode([
        'error' => 'File is not readable',
        'path' => $favoritesFile
    ]);
    exit;
}

$content = file_get_contents($favoritesFile);
if ($content === false) {
    echo json_encode([
        'error' => 'Could not read file',
        'path' => $favoritesFile
    ]);
    exit;
}

echo $content;
?> 