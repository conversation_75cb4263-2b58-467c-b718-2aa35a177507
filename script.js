// Fancybox global setup
Fancybox.bind('[data-fancybox="gallery"]', {
    compact: false,
    idle: false,
    autoStart: true,
    animated: false,
    showClass: false,
    hideClass: false,
    hideScrollbar: false,
    dragToClose: false,
    contentClick: false,
    Carousel: {
        // autoPlay: true,
    },
    Fullscreen: {
        autoStart: false,
    },
    Images: {
        zoom: false,
    },
    Thumbs: {
        type: 'classic',
        showOnStart: false,
        hideOnScroll: false,
    },
    Html: {
        videoTpl: '<video class="fancybox__html5video" controls controlsList="nodownload" poster="{{poster}}">' +
            '<source src="{{src}}" type="{{format}}" />' +
            'Sorry, your browser doesn\'t support embedded videos.' +
            '</video>',
    },
    // 添加视频特定配置
    Video: {
        autoplay: false,
        ratio: 16/9,
    }
});

// 在Fancybox事件中添加视频加载处理
Fancybox.Plugins.Html.startLoad = function(slide) {
    // 显示加载指示器
    slide.$el.classList.add('is-loading');
    
    const $content = slide.$content;
    
    if ($content && $content.tagName === 'VIDEO') {
        const video = $content;
        
        video.addEventListener('loadedmetadata', function() {
            // 视频元数据加载完成
            slide.$el.classList.remove('is-loading');
        });
        
        video.addEventListener('error', function() {
            // 视频加载错误
            slide.$el.classList.remove('is-loading');
            slide.$el.classList.add('has-error');
        });
    }
};

var elem = document.querySelector('#imageRow');

// This function seems related to an external theme or specific post integration.
// If not directly part of this gallery's core logic, ensure it doesn't conflict.
// For now, I'll keep it as it was in your original full code.
function adjustColumns() {
    var fancy = document.getElementsByClassName("data-fancy");
    // console.log(fancy); // Original console log
    for (var i = 0; i < fancy.length; i++) {
        fancy[i].setAttribute("data-fancybox","gallery");
    }
    var riproStars = document.getElementsByClassName("ripro-star");
    if (riproStars.length > 1) {
        var sideBarStar = document.getElementsByClassName("side-bar-star")[0];
        if (sideBarStar) {
            var mainContentStar = (riproStars[0] !== sideBarStar ? riproStars[0] : (riproStars[1] || null) );
            if (mainContentStar) {
                 sideBarStar.setAttribute("data-postid",mainContentStar.getAttribute("data-postid"));
            }
        }
    }
}
window.addEventListener("load", adjustColumns);
window.addEventListener("resize", adjustColumns);
// window.addEventListener("scroll", adjustColumns); // Scroll might be too frequent for this

const FOLDER_BASE_PATH = ''; // Set to 'your_base_folder' if YYYY-MM folders are not at web root

class ImageGallery {
    constructor() {
        console.log('[Gallery CONSTRUCTOR] Initializing ImageGallery...');
        this.folderList = [];
        this.currentIndex = 0;
        this.foldersPerLoad = 5;
        this.loadedImages = new Set();
        this.currentColumns = 3;
        this.isViewingFavorites = false;
        this.isViewingTags = false; // 新增：是否在查看标签模式

        this.mainFolderSelectorEl = document.getElementById('mainFolderSelectorEl');
        this.subFolderSelectorEl = document.getElementById('subFolderSelectorEl');
        this.favoritesSelectorEl = document.getElementById('favoritesSelectorEl');
        this.tagsSelectorEl = document.getElementById('tagsSelectorEl');
        this.taggedFoldersSelectorEl = document.getElementById('taggedFoldersSelectorEl');
        
        console.log('[CONSTRUCTOR] mainFolderSelectorEl found in DOM:', this.mainFolderSelectorEl);
        console.log('[CONSTRUCTOR] subFolderSelectorEl found in DOM:', this.subFolderSelectorEl);

        this.criticalElementsMissing = false;
        if (!this.mainFolderSelectorEl || !this.subFolderSelectorEl) {
            console.error("[CONSTRUCTOR FATAL ERROR] Dropdown select elements not found in DOM.");
            this.criticalElementsMissing = true; 
        }

        this.initialMonthFromURL = null;
        this.initialSubfolderFromURL = null;
        this.selectedMainFolder = null;
        this.selectedSubfolder = 'all';

        this.isLoading = false; 
        this.noMoreData = false; 
        this.msnry = null; 
        this.isInitialLoad = true; 
        this.abortController = null;
    }

    // --- Helper Functions ---
    truncateName(name, maxLength = 35) {
        if (typeof name !== 'string') return '';
        if (name.length > maxLength) {
            return name.substring(0, maxLength - 3) + "...";
        }
        return name;
    }

    decodeFolderName(name) {
        if (!name) return name; 
        let originalNameForLog = name;
        let potentialDecodedName = name;
        try {
            potentialDecodedName = decodeURIComponent(name);
        } catch (e) {
            // console.warn(`[decodeFolderName] URIError during decoding of: '${originalNameForLog}'. Treating as plain. Original error:`, e.message);
            potentialDecodedName = name; 
        }
        // let charCodes = Array.from(potentialDecodedName).map(char => char.charCodeAt(0));
        // let charCodesToLog = charCodes.length > 40 ? `[${charCodes.slice(0,20).join(',')}, ..., ${charCodes.slice(-20).join(',')}]` : `[${charCodes.join(',')}]`;
        // console.log(`[decodeFolderName] Original: '${originalNameForLog}', Processed: '${potentialDecodedName}', CharCodes: ${charCodesToLog}, Length: ${potentialDecodedName.length}`);
        return potentialDecodedName;
    }
    
    updateURL() { 
        const params = new URLSearchParams();
        
        // 添加视图模式参数
        if (this.isViewingTags) {
            params.set('view', 'tags');
            if (this.tagsSelectorEl.value) {
                params.set('tag', this.tagsSelectorEl.value);
            }
            if (this.taggedFoldersSelectorEl.value && this.taggedFoldersSelectorEl.value !== 'all') {
                params.set('tagged_folder', this.taggedFoldersSelectorEl.value);
            }
        } else if (this.isViewingFavorites) {
            params.set('view', 'favorites');
            if (this.favoritesSelectorEl.value) {
                params.set('favorite', this.favoritesSelectorEl.value);
            }
        } else {
            // 普通视图模式
            if (this.selectedMainFolder) {
                params.set('month', encodeURIComponent(this.selectedMainFolder)); 
            }
            if (this.selectedSubfolder && this.selectedSubfolder !== 'all') {
                params.set('subfolder', this.selectedSubfolder); 
            }
        }
        
        const newRelativePathQuery = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
        history.pushState(
            { 
                view: this.isViewingTags ? 'tags' : (this.isViewingFavorites ? 'favorites' : 'normal'),
                month: this.selectedMainFolder, 
                subfolder: this.selectedSubfolder,
                tag: this.tagsSelectorEl.value,
                tagged_folder: this.taggedFoldersSelectorEl.value,
                favorite: this.favoritesSelectorEl.value
            }, 
            '', 
            newRelativePathQuery
        );
        console.log('[updateURL] URL updated to:', newRelativePathQuery);
    }

    // --- Initialization ---
    async initialize() {
        if (this.criticalElementsMissing) {
            console.error("[INITIALIZE ABORTED] Critical HTML elements for dropdowns were missing.");
            this.showStatusMessage("Error: UI components for folder selection are missing.");
            return;
        }
        console.log('[Gallery INITIALIZE START]');
        
        // 初始化所有DOM元素引用
        this.viewNormalBtn = document.getElementById('viewNormalBtn');
        this.viewFavoritesBtn = document.getElementById('viewFavoritesBtn');
        this.viewTagsBtn = document.getElementById('viewTagsBtn');
        
        // 初始化标签和收藏功能
        await this.initializeFavorites();
        await this.initializeTags();
        await this.initializeNormalView();
        
        const urlParams = new URLSearchParams(window.location.search);
        
        // 解析视图模式
        const viewMode = urlParams.get('view');
        if (viewMode === 'tags') {
            this.setViewMode('tags');
            const tagName = urlParams.get('tag');
            if (tagName && this.tagsSelectorEl) {
                this.tagsSelectorEl.value = tagName;
                const taggedFolder = urlParams.get('tagged_folder');
                if (taggedFolder && this.taggedFoldersSelectorEl) {
                    await this.loadTaggedFolders(tagName, () => {
                        const found = Array.from(this.taggedFoldersSelectorEl.options).some(opt => opt.value === taggedFolder);
                        if (found) {
                            this.taggedFoldersSelectorEl.value = taggedFolder;
                            const {month, subfolder} = JSON.parse(taggedFolder);
                            this.navigateToTaggedFolder({month, subfolder});
                        }
                    });
                } else {
                    await this.loadTaggedFolders(tagName);
                }
            }
        } else if (viewMode === 'favorites') {
            this.setViewMode('favorites');
            const favorite = urlParams.get('favorite');
            if (favorite && this.favoritesSelectorEl) {
                this.favoritesSelectorEl.value = favorite;
                const {month, subfolder} = JSON.parse(favorite);
                await this.navigateToFavorite({month, subfolder});
            }
        } else {
            this.setViewMode('normal');
            this.initialMonthFromURL = this.decodeFolderName(urlParams.get('month')); 
            this.initialSubfolderFromURL = urlParams.get('subfolder'); 
        }

        try {
            await this.populateMainFolderDropdown(); 
            console.log(`[INITIALIZE] After populateMainFolderDropdown: selectedMainFolder (decoded)='${this.selectedMainFolder}'`);

            if (!this.selectedMainFolder && !this.isViewingTags && !this.isViewingFavorites) {
                this.showStatusMessage('Please select a month or no folders found.');
                this.toggleLoadMoreButton(false);
                this.isInitialLoad = false;
                console.log('[INITIALIZE END] No main folder selected.');
                return;
            }

            await this.fetchSubFolderList(); 
            console.log(`[INITIALIZE] After fetchSubFolderList: this.folderList has ${this.folderList.length} items.`);
            
            await this.populateSubFolderDropdown();
            console.log(`[INITIALIZE] After populateSubFolderDropdown: selectedSubfolder (pathSegment or 'all')='${this.selectedSubfolder}'`);
            
            // Add sort selector event listener
            const sortSelector = document.getElementById('sortSelectorEl');
            if (sortSelector) {
                sortSelector.addEventListener('change', async () => {
                    await this.populateSubFolderDropdown();
                    await this.reloadData();
                });
            }
            
            if (!this.isViewingTags && !this.isViewingFavorites) {
                await this.reloadData(); 
            }
            
            this.setupColumnSelector();
            this.isInitialLoad = false; 
            console.log('[INITIALIZE END] Successfully initialized.');

        } catch (error) {
            console.error('[INITIALIZE ERROR]', error);
            this.showStatusMessage('Error initializing the gallery.');
            this.isInitialLoad = false;
        }
    }

    // Favorites functionality
    async initializeFavorites() {
        this.favoriteBtn = document.getElementById('favoriteBtn');
        this.nextFolderBtn = document.getElementById('nextFolderBtn');
        this.favoritesModal = document.querySelector('.favorites-modal');
        this.modalBackdrop = document.querySelector('.modal-backdrop');
        this.favoritesList = document.querySelector('.favorites-list');
        this.viewFavoritesBtn = document.getElementById('viewFavoritesBtn');
        
        // Load initial favorites
        await this.loadFavorites();
        
        // Setup event listeners
        this.favoriteBtn.addEventListener('click', () => {
            if (this.selectedSubfolder === 'all') {
                alert('请先选择一个文件夹再收藏');
                return;
            }
            this.toggleFavorite();
        });

        // Favorites dropdown change event
        this.favoritesSelectorEl.addEventListener('change', async (e) => {
            if (!e.target.value) return;
            
            const {month, subfolder} = JSON.parse(e.target.value);
            // 清空当前内容
            this.clearGalleryContent();
            // 直接加载收藏夹内容，不更新其他UI元素
            const images = await this.loadImagesFromFavoriteFolder(month, subfolder);
            if (images.length > 0) {
                this.displayImages(images);
            } else {
                this.showStatusMessage('No images found in this favorite folder.');
            }
        });

        // View favorites mode toggle
        this.viewFavoritesBtn.addEventListener('click', async () => {
            if (this.isViewingFavorites) {
                this.setViewMode('normal');
                // Reset to normal view
                if (this.selectedMainFolder) {
                    await this.reloadData();
                } else {
                    this.clearGalleryContent();
                    this.showStatusMessage('Please select a month.');
                }
            } else {
                this.setViewMode('favorites');
                // Clear selections
                this.mainFolderSelectorEl.value = '';
                this.subFolderSelectorEl.value = 'all';
                this.favoritesSelectorEl.value = '';
                
                // Load all favorites
                await this.loadFavoritesView();
            }
            this.updateURL();
        });
        
        // Next folder button functionality
        this.nextFolderBtn.addEventListener('click', () => {
            this.goToNextFolder();
        });

        // Update favorite button state
        this.updateFavoriteButtonState();
    }
    
    async loadFavorites() {
        try {
            const response = await fetch('favorites.json');
            const data = await response.json();
            this.favorites = data.favorites || [];
            this.updateFavoritesDropdown();
        } catch (error) {
            console.error('Error loading favorites:', error);
            this.favorites = [];
        }
    }
    
    updateFavoritesDropdown() {
        const select = this.favoritesSelectorEl;
        select.innerHTML = '<option value="">Select from favorites</option>';
        
        // Sort favorites by timestamp in descending order
        const sortedFavorites = [...this.favorites].sort((a, b) => 
            new Date(b.timestamp) - new Date(a.timestamp)
        );

        sortedFavorites.forEach(favorite => {
            const option = document.createElement('option');
            option.value = JSON.stringify({month: favorite.month, subfolder: favorite.subfolder});
            option.textContent = `${favorite.displayName} (${favorite.month})`;
            select.appendChild(option);
        });
    }
    
    async toggleFavorite() {
        if (!this.selectedMainFolder || !this.selectedSubfolder || this.selectedSubfolder === 'all') return;
        
        const id = `${this.selectedMainFolder}_${this.selectedSubfolder}`;
        const isFavorited = this.favorites.some(f => f.id === id);
        
        if (!isFavorited) {
            // Add to favorites
            this.favorites.push({
                id,
                month: this.selectedMainFolder,
                subfolder: this.selectedSubfolder,
                displayName: this.folderList.find(f => f.pathSegment === this.selectedSubfolder)?.displayName || this.selectedSubfolder,
                timestamp: new Date().toISOString()
            });
        } else {
            // Remove from favorites
            this.favorites = this.favorites.filter(f => f.id !== id);
        }
        
        try {
            // Save to file using POST method
            const response = await fetch('save_favorites.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ favorites: this.favorites })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            this.updateFavoriteButtonState();
        } catch (error) {
            console.error('Error saving favorites:', error);
            alert('保存收藏失败，请检查文件权限');
        }
    }
    
    updateFavoriteButtonState() {
        if (!this.selectedMainFolder || !this.selectedSubfolder || this.selectedSubfolder === 'all') {
            this.favoriteBtn.classList.remove('active');
            return;
        }
        
        const id = `${this.selectedMainFolder}_${this.selectedSubfolder}`;
        const isFavorited = this.favorites.some(f => f.id === id);
        
        if (isFavorited) {
            this.favoriteBtn.classList.add('active');
        } else {
            this.favoriteBtn.classList.remove('active');
        }
    }
    
    showFavoritesModal() {
        // Update favorites list
        this.favoritesList.innerHTML = '';
        if (this.favorites.length === 0) {
            this.favoritesList.innerHTML = '<div style="text-align: center; padding: 20px;">暂无收藏</div>';
        } else {
            const ul = document.createElement('ul');
            ul.className = 'favorites-list';
            
            this.favorites.forEach(favorite => {
                const li = document.createElement('li');
                li.innerHTML = `
                    <span>${favorite.displayName}</span>
                    <button onclick="event.stopPropagation();">×</button>
                `;
                
                // Click to navigate
                li.addEventListener('click', () => {
                    this.navigateToFavorite(favorite);
                    this.hideFavoritesModal();
                });
                
                // Remove favorite
                li.querySelector('button').addEventListener('click', async (e) => {
                    e.stopPropagation();
                    await this.removeFavorite(favorite.id);
                    li.remove();
                    if (this.favorites.length === 0) {
                        this.favoritesList.innerHTML = '<div style="text-align: center; padding: 20px;">暂无收藏</div>';
                    }
                });
                
                ul.appendChild(li);
            });
            
            this.favoritesList.appendChild(ul);
        }
        
        this.favoritesModal.style.display = 'block';
        this.modalBackdrop.style.display = 'block';
    }
    
    hideFavoritesModal() {
        this.favoritesModal.style.display = 'none';
        this.modalBackdrop.style.display = 'none';
    }
    
    async removeFavorite(id) {
        try {
            this.favorites = this.favorites.filter(f => f.id !== id);
            
            // Save to file using POST method
            const response = await fetch('save_favorites.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ favorites: this.favorites })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            this.updateFavoriteButtonState();
        } catch (error) {
            console.error('Error removing favorite:', error);
            alert('删除收藏失败，请检查文件权限');
        }
    }
    
    async navigateToFavorite(favorite) {
        // Set month
        this.mainFolderSelectorEl.value = favorite.month;
        await this.handleMainFolderChange();
        
        // Set subfolder
        this.subFolderSelectorEl.value = favorite.subfolder;
        await this.handleSubFolderChange();
    }

    async goToNextFolder() {
        console.log('[goToNextFolder] Starting next folder navigation');
        
        if (this.isViewingFavorites) {
            // 如果在查看收藏模式，切换收藏下拉框
            await this.goToNextFavorite();
        } else if (this.isViewingTags) {
            // 如果在标签模式，切换标签文件夹下拉框
            await this.goToNextTaggedFolder();
        } else {
            // 如果在普通模式，切换子文件夹
            await this.goToNextSubfolder();
        }
        
        // 滚动到页面顶部
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
        console.log('[goToNextFolder] Scrolled to top');
    }

    async goToNextFavorite() {
        console.log('[goToNextFavorite] Switching to next favorite');
        const favoritesSelect = this.favoritesSelectorEl;
        const options = Array.from(favoritesSelect.options).filter(option => option.value !== '');
        
        if (options.length === 0) {
            console.log('[goToNextFavorite] No favorites available');
            return;
        }

        let currentIndex = -1;
        if (favoritesSelect.value) {
            currentIndex = options.findIndex(option => option.value === favoritesSelect.value);
        }

        // 切换到下一个，如果到末尾则回到开头
        const nextIndex = (currentIndex + 1) % options.length;
        favoritesSelect.value = options[nextIndex].value;

        // 触发change事件
        const changeEvent = new Event('change', { bubbles: true });
        favoritesSelect.dispatchEvent(changeEvent);
    }

    async goToNextTaggedFolder() {
        console.log('[goToNextTaggedFolder] Switching to next tagged folder');
        const taggedFoldersSelect = this.taggedFoldersSelectorEl;
        const options = Array.from(taggedFoldersSelect.options).filter(option => option.value !== 'all');
        
        if (options.length === 0) {
            console.log('[goToNextTaggedFolder] No tagged folders available');
            alert('没有更多标签文件夹了');
            return;
        }

        let currentIndex = -1;
        if (taggedFoldersSelect.value && taggedFoldersSelect.value !== 'all') {
            currentIndex = options.findIndex(option => option.value === taggedFoldersSelect.value);
        }

        // 切换到下一个，如果到末尾则回到开头
        const nextIndex = (currentIndex + 1) % options.length;
        taggedFoldersSelect.value = options[nextIndex].value;

        // 触发change事件
        const changeEvent = new Event('change', { bubbles: true });
        taggedFoldersSelect.dispatchEvent(changeEvent);
    }

    async goToNextSubfolder() {
        console.log('[goToNextSubfolder] Switching to next subfolder');
        const subfolderSelect = this.subFolderSelectorEl;
        const options = Array.from(subfolderSelect.options);
        
        if (options.length <= 1) { // 只有"All Sub-folders"选项
            console.log('[goToNextSubfolder] No subfolders available');
            return;
        }

        let currentIndex = 0; // 默认从"All Sub-folders"开始
        if (subfolderSelect.value && subfolderSelect.value !== 'all') {
            currentIndex = options.findIndex(option => option.value === subfolderSelect.value);
        }

        // 切换到下一个
        let nextIndex = currentIndex + 1;
        if (nextIndex >= options.length) {
            // 如果到末尾，回到"All Sub-folders"
            nextIndex = 0;
        }

        subfolderSelect.value = options[nextIndex].value;

        // 触发change事件
        const changeEvent = new Event('change', { bubbles: true });
        subfolderSelect.dispatchEvent(changeEvent);
    }

    async handleSubFolderChange() {
        const newSubfolder = this.subFolderSelectorEl.value;
        console.log(`[handleSubFolderChange] New subfolder selected: '${newSubfolder}', current: '${this.selectedSubfolder}'`);
        if (this.selectedSubfolder === newSubfolder && !this.isInitialLoad) return;

        this.selectedSubfolder = newSubfolder;
        console.log(`[handleSubFolderChange] State updated: selectedSubfolder='${this.selectedSubfolder}'`);
        this.updateURL();
        this.updateFavoriteButtonState();
        
        // 确保重新加载数据
        await this.reloadData();
    }

    showStatusMessage(message) { 
        console.log('[showStatusMessage]', message);
        const galleryRow = document.getElementById('imageRow');
        if (galleryRow) {
            galleryRow.innerHTML = `<p style="text-align:center;">${message}</p>`;
        }
    }

    toggleLoadMoreButton(show) {
        const loadMoreBtnEl = document.getElementById('loadMoreBtn');
        if (loadMoreBtnEl) {
            // console.log(`[toggleLoadMoreButton] Show: ${show}`);
            loadMoreBtnEl.style.display = show ? 'block' : 'none';
        }
    }

    // --- Dropdown Population & Event Handling ---
    async populateMainFolderDropdown() {
        console.log('[populateMainFolderDropdown] START');
        await this.fetchAvailableMainFolders(); 
        this.mainFolderSelectorEl.innerHTML = ''; 

        if (this.availableMainFolders.length === 0) {
            console.log('[populateMainFolderDropdown] No available main folders found.');
            const option = document.createElement('option');
            option.value = "";
            option.textContent = 'No date folders found';
            this.mainFolderSelectorEl.appendChild(option);
            this.selectedMainFolder = null;
            return;
        }

        this.availableMainFolders.forEach(folderName => { // folderName is decoded and trimmed
            const option = document.createElement('option');
            option.value = folderName; 
            let displayText = this.truncateName(folderName);
            let sanitizedDisplayText = displayText.replace(/[\x00-\x1F\x7F]/g, "").trim(); 
            option.textContent = sanitizedDisplayText || "\u00A0"; 
            // console.log(`[populateMainFolderDropdown] OPTION: value='${folderName}', textContent='${option.textContent}'`);
            this.mainFolderSelectorEl.appendChild(option);
        });
        
        let defaultMainFolder = this.availableMainFolders.length > 0 ? this.availableMainFolders[0] : null;
        if (this.isInitialLoad && this.initialMonthFromURL && this.availableMainFolders.includes(this.initialMonthFromURL)) {
            defaultMainFolder = this.initialMonthFromURL;
        }
        
        if (defaultMainFolder) this.mainFolderSelectorEl.value = defaultMainFolder;
        this.selectedMainFolder = this.mainFolderSelectorEl.value; // Will be a decoded name

        console.log(`[populateMainFolderDropdown] END - Selected main folder: '${this.selectedMainFolder}'`);

        this.mainFolderSelectorEl.removeEventListener('change', this.handleMainFolderChangeBound);
        this.handleMainFolderChangeBound = this.handleMainFolderChange.bind(this);
        this.mainFolderSelectorEl.addEventListener('change', this.handleMainFolderChangeBound);
    }
    
    async handleMainFolderChange() { // Bound to this
        const newMonth = this.mainFolderSelectorEl.value; // Direct value is decoded name
        console.log(`[handleMainFolderChange] New month selected: '${newMonth}', current: '${this.selectedMainFolder}'`);
        if (this.selectedMainFolder === newMonth && !this.isInitialLoad) return;

        this.selectedMainFolder = newMonth;
        this.selectedSubfolder = 'all'; 
        console.log(`[handleMainFolderChange] State updated: selectedMainFolder='${this.selectedMainFolder}', selectedSubfolder='${this.selectedSubfolder}'`);

        this.updateURL(); 
        
        await this.fetchSubFolderList(); 
        console.log(`[handleMainFolderChange] After fetchSubFolderList for new month: this.folderList has ${this.folderList.length} items.`);
        await this.populateSubFolderDropdown(); 
        
        await this.reloadData(); 
    }

    async populateSubFolderDropdown() {
        console.log('[populateSubFolderDropdown] START - Current selectedSubfolder (pathSegment or "all") state:', this.selectedSubfolder);
        const el = this.subFolderSelectorEl;
        el.innerHTML = ''; 

        const allOption = document.createElement('option');
        allOption.value = 'all';
        allOption.textContent = 'All Sub-folders'; 
        el.appendChild(allOption);

        if (this.folderList && this.folderList.length > 0) { 
            console.log('[populateSubFolderDropdown] Populating with subfolder objects (count):', this.folderList.length);
            
            // Sort the folderList based on the selected sort method
            const sortMethod = document.getElementById('sortSelectorEl').value;
            if (sortMethod === 'date') {
                this.folderList.sort((a, b) => b.date - a.date); // 降序排列
            } else if (sortMethod === 'name') {
                this.folderList.sort((a, b) => a.displayName.localeCompare(b.displayName)); // 升序排列
            }
            
            this.folderList.forEach(folderObj => { 
                const option = document.createElement('option');
                option.value = folderObj.pathSegment; 
                let displayText = this.truncateName(folderObj.displayName);
                let sanitizedDisplayText = displayText.replace(/[\x00-\x1F\x7F]/g, "").trim();
                option.textContent = sanitizedDisplayText || "\u00A0"; 
                el.appendChild(option);
            });
        } else {
            console.log('[populateSubFolderDropdown] No subfolder objects in this.folderList.');
        }
        
        let valueToSet = 'all';
        if (this.isInitialLoad && this.initialSubfolderFromURL) { // initialSubfolderFromURL is raw pathSegment from URL
            if (this.initialSubfolderFromURL === 'all' || this.folderList.some(f => f.pathSegment === this.initialSubfolderFromURL)) {
                valueToSet = this.initialSubfolderFromURL;
            } else {
                 console.log(`[populateSubFolderDropdown] Initial subfolder pathSegment '${this.initialSubfolderFromURL}' not found, defaulting to 'all'.`);
            }
        } else if (!this.isInitialLoad && this.selectedSubfolder) { 
             if (this.selectedSubfolder === 'all' || this.folderList.some(f => f.pathSegment === this.selectedSubfolder)) {
                valueToSet = this.selectedSubfolder;
            } else {
                console.log(`[populateSubFolderDropdown] Current selectedSubfolder pathSegment '${this.selectedSubfolder}' not found, defaulting to 'all'.`);
            }
        }
        el.value = valueToSet;
        this.selectedSubfolder = el.value; // This is now a pathSegment or 'all'

        console.log(`[populateSubFolderDropdown] END - Selected subfolder (pathSegment or 'all'): '${this.selectedSubfolder}'`);

        el.removeEventListener('change', this.handleSubFolderChangeBound);
        this.handleSubFolderChangeBound = this.handleSubFolderChange.bind(this);
        el.addEventListener('change', this.handleSubFolderChangeBound);
    }

    // --- Fetching Logic ---
    async fetchAvailableMainFolders() {
        console.log('[fetchAvailableMainFolders] START');
        try {
            const directoryToFetch = FOLDER_BASE_PATH ? `/${FOLDER_BASE_PATH}/` : `/`;
            // console.log(`[fetchAvailableMainFolders] Fetching from: ${directoryToFetch}`);
            const response = await fetch(directoryToFetch);
            // console.log(`[fetchAvailableMainFolders] Response status: ${response.status}, ok: ${response.ok}`);
            if (!response.ok) throw new Error(`HTTP error! Status: ${response.status} for ${directoryToFetch}`);
            const text = await response.text();
            const parser = new DOMParser();
            const doc = parser.parseFromString(text, 'text/html');
            const links = doc.querySelectorAll('a');
            // console.log(`[fetchAvailableMainFolders] Found ${links.length} <a> tags from ${directoryToFetch}`);
            
            const yearMonthPattern = /^\d{4}-\d{2}\/$/; 
            let fetchedFolders = [];
            links.forEach(link => {
                const href = link.getAttribute('href');
                if (href && href.endsWith('/') && href !== '../' && !href.startsWith('?')) {
                    let folderName = href.slice(0, -1);
                    if (!yearMonthPattern.test(href)) { 
                        folderName = this.decodeFolderName(folderName);
                    }
                    folderName = folderName.trim(); 
                    if (folderName) fetchedFolders.push(folderName); 
                }
            });
            fetchedFolders.sort((a, b) => b.localeCompare(a)); 
            this.availableMainFolders = fetchedFolders;
            console.log('[fetchAvailableMainFolders] END - Available main folders (decoded, trimmed):', JSON.parse(JSON.stringify(this.availableMainFolders.slice(0,10))));
        } catch (error) { 
            console.error('[fetchAvailableMainFolders] ERROR CAUGHT:', error); 
            this.availableMainFolders = [];
        }
    }

    async fetchSubFolderList() {
        console.log(`[fetchSubFolderList] START for main folder (decoded): '${this.selectedMainFolder}'`);
        if (!this.selectedMainFolder) { this.folderList = []; return; }
        try {
            const encodedMainFolder = encodeURIComponent(this.selectedMainFolder);
            const mainFolderPathToFetch = FOLDER_BASE_PATH 
                ? `/${FOLDER_BASE_PATH}/${encodedMainFolder}` 
                : `/${encodedMainFolder}`;
            
            console.log(`[fetchSubFolderList] Fetching subfolders from URL: ${mainFolderPathToFetch}/`);
            const response = await fetch(mainFolderPathToFetch + '/'); 
            if (!response.ok) throw new Error(`HTTP error! Status: ${response.status} for ${mainFolderPathToFetch}/ (Code: ${response.status})`);
            const text = await response.text();
            const parser = new DOMParser();
            const doc = parser.parseFromString(text, 'text/html');
            const links = doc.querySelectorAll('a');

            const foldersObjects = Array.from(links).map(link => {
                const dateText = link.nextSibling?.textContent?.trim();
                const dateMatch = dateText?.match(/(\d{2}-\w{3}-\d{4}\s+\d{2}:\d{2})/);
                if (dateMatch) {
                    let hrefVal = link.getAttribute('href');
                    let segment1 = '';
                    if (hrefVal && hrefVal.endsWith('/')) { 
                        segment1 = hrefVal.slice(0, -1); 
                    } else { return null; }

                    if (segment1 && segment1 !== '' && segment1 !== '.' && segment1 !== '..' && !segment1.startsWith('?')) {
                        let displayName = this.decodeFolderName(segment1).trim(); 
                        let trimmedSegment1 = segment1.trim(); 
                        if (!displayName) return null; 
                        return { 
                            displayName: displayName, 
                            pathSegment: trimmedSegment1,  
                            dateStr: dateMatch[1], 
                            date: new Date(dateMatch[1]) 
                        };
                    }
                }
                return null;
            }).filter(item => item !== null);
            
            foldersObjects.sort((a, b) => a.displayName.localeCompare(b.displayName));
            this.folderList = foldersObjects; 
            console.log('[fetchSubFolderList] END - Sorted subfolder list (first 5 objects):', JSON.parse(JSON.stringify(this.folderList.slice(0,5))));

        } catch (error) { 
            console.error(`[fetchSubFolderList] ERROR for main folder '${this.selectedMainFolder}':`, error);
            this.folderList = [];
        }
    }

    async loadImagesFromFolder(subfolderPathSegment) {
        // Cancel any ongoing requests
        this.cancelOngoingRequests();
        
        // Create new AbortController for this request
        this.abortController = new AbortController();
        
        try {
            console.log(`[loadImagesFromFolder] START - Main (decoded): '${this.selectedMainFolder}', Sub PathSegment: '${subfolderPathSegment}'`);
            if (!this.selectedMainFolder || !subfolderPathSegment) { return []; }
            try {
                const encodedMain = encodeURIComponent(this.selectedMainFolder);
                const pathForImageDirectory = FOLDER_BASE_PATH 
                    ? `/${FOLDER_BASE_PATH}/${encodedMain}/${subfolderPathSegment}` 
                    : `/${encodedMain}/${subfolderPathSegment}`;
                
                console.log(`[loadImagesFromFolder] Fetching image list from URL: ${pathForImageDirectory}/`);
                const response = await fetch(pathForImageDirectory + '/', {
                    signal: this.abortController.signal
                }); 
                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status} for ${pathForImageDirectory}/ (Code: ${response.status})`);
                const text = await response.text();
                const parser = new DOMParser();
                const doc = parser.parseFromString(text, 'text/html');
                const links = doc.querySelectorAll('a');
                
                const imageUrls = Array.from(links)
                    .map(link => link.getAttribute('href'))
                    .filter(href => {
                        if (!href || href === '../' || href.startsWith('?') || href.startsWith('/')) return false;
                        const decodedHrefForTest = this.decodeFolderName(href); 
                        return /\.(jpg|jpeg|png|gif|mp4|avi|webm|mov|mkv)$/i.test(decodedHrefForTest);
                    }) 
                    .map(href => {
                        let finalHrefSegment = href;
                        if (!href.includes('%') && /[^\w.\-\/]/.test(href)) {
                            finalHrefSegment = encodeURIComponent(href);
                        }
                        return `${pathForImageDirectory}/${finalHrefSegment}`;
                    }); 
                console.log(`[loadImagesFromFolder] END - Found ${imageUrls.length} image URLs (first 5):`, JSON.parse(JSON.stringify(imageUrls.slice(0,5))));
                return imageUrls;
            } catch (error) {
                if (error.name === 'AbortError') {
                    console.log('[loadImagesFromFolder] Request was aborted');
                    return [];
                }
                console.error(`[loadImagesFromFolder] ERROR for Main: '${this.selectedMainFolder}', SubPathSegment: '${subfolderPathSegment}'. Error:`, error);
                return [];
            }    
        } catch (error) {
            console.error('[loadImagesFromFolder] Error:', error);
            return [];
        }
    }
    
    // --- Core Data Reloading and Display Logic ---
    clearGalleryContent() {
        console.log('[clearGalleryContent] Clearing gallery and destroying Masonry.');
        const gallery = document.getElementById('imageRow');
        if (this.msnry) { 
            try { this.msnry.destroy(); } catch(e) { console.warn("Error destroying Masonry:", e); }
            this.msnry = null;
        }
        if (gallery) gallery.innerHTML = '';
        this.loadedImages.clear();
    }

    async reloadData() {
        console.log(`[reloadData] START - Main: '${this.selectedMainFolder}', Subfolder PathSegment/Keyword: '${this.selectedSubfolder}'`);
        
        // If in favorites view mode, load favorites instead
        if (this.isViewingFavorites) {
            await this.loadFavoritesView();
            return;
        }
        
        // If in tags view mode, load tags instead
        if (this.isViewingTags) {
            await this.loadTagsView();
            return;
        }

        this.clearGalleryContent();
        this.currentIndex = 0;
        this.noMoreData = false;

        if (!this.selectedMainFolder) {
            this.showStatusMessage("Please select a month.");
            this.toggleLoadMoreButton(false);
            console.log('[reloadData] END - No main folder.');
            return;
        }

        let subfolderPathSegmentsToProcess = []; 
        if (this.selectedSubfolder && this.selectedSubfolder !== 'all') {
            const specificPathSegment = this.selectedSubfolder;
            if (this.folderList.some(f => f.pathSegment === specificPathSegment)) {
                subfolderPathSegmentsToProcess = [specificPathSegment]; 
                this.noMoreData = true; 
                console.log(`[reloadData] Specific subfolder selected. PathSegment: '${specificPathSegment}'. noMoreData=true.`);
            } else { /* ... error handling ... */ return; }
        } else { 
             subfolderPathSegmentsToProcess = this.folderList.map(f => f.pathSegment); 
             this.noMoreData = (subfolderPathSegmentsToProcess.length === 0);
             console.log(`[reloadData] "All" subfolders. PathSegments count: ${subfolderPathSegmentsToProcess.length}. noMoreData=${this.noMoreData}.`);
        }
        
        const initialBatch = subfolderPathSegmentsToProcess.slice(0, 
            (this.selectedSubfolder && this.selectedSubfolder !== 'all') ? subfolderPathSegmentsToProcess.length : this.foldersPerLoad
        );
        console.log('[reloadData] Initial batch of subfolder pathSegments to load:', JSON.parse(JSON.stringify(initialBatch)));

        if (initialBatch.length > 0) {
            await this.loadAndDisplayImages(initialBatch); 
        } else {
            console.log('[reloadData] No initial batch of subfolders to load. Displaying "no images" message.');
             this.showStatusMessage(this.selectedSubfolder && this.selectedSubfolder !== 'all' ? `No images found in the selected sub-folder.` : `No images found for ${this.selectedMainFolder}.`);
        }
        this.currentIndex = initialBatch.length;

        if (!(this.selectedSubfolder && this.selectedSubfolder !== 'all')) {
            this.noMoreData = (this.currentIndex >= subfolderPathSegmentsToProcess.length);
        }
        console.log(`[reloadData] After initial load: currentIndex=${this.currentIndex} (of ${subfolderPathSegmentsToProcess.length}), noMoreData=${this.noMoreData}`);
        
        this.setupLoadMoreButton(); 
        console.log('[reloadData] END');
    }

    async loadAndDisplayImages(subfolderPathSegmentsToLoad) { // Expects array of pathSegments
        console.log('[loadAndDisplayImages] Loading pathSegments:', JSON.parse(JSON.stringify(subfolderPathSegmentsToLoad)));
        this.isLoading = true;
        for (const pathSegment of subfolderPathSegmentsToLoad) { 
            if (pathSegment) {
                const images = await this.loadImagesFromFolder(pathSegment); 
                this.displayImages(images); 
            }
        }
        this.isLoading = false;
    }
    displayImages(imageUrls) { 
        console.log(`[displayImages] Received ${imageUrls.length} image/video URLs to display.`);
        const gallery = document.getElementById('imageRow');
        if (!gallery) { console.error("[displayImages] Gallery #imageRow NOT FOUND!"); return; }
        
        const fragment = document.createDocumentFragment(); 
        let newImagesAddedCount = 0;
        
        // 保存当前滚动位置
        const scrollPosition = window.scrollY;
        
        // 计算列宽度类
        const columnClassNumber = 12 / this.currentColumns;
        
        imageUrls.forEach(url => {
            if (!this.loadedImages.has(url)) {
                newImagesAddedCount++;
                const customColumn = document.createElement('div');
                // 应用正确的列类
                customColumn.className = `custom-column col-12 col-sm-${columnClassNumber} col-md-${columnClassNumber} col-xl-${columnClassNumber}`;
                
                // 检查是否为视频文件
                const isVideo = this.isVideoFile(url);
                
                if (isVideo) {
                    // 视频元素
                    customColumn.innerHTML = `
                        <div class="image-wrapper video-wrapper">
                            <a href="${url}" class="data-fancy" data-fancybox="gallery" data-type="video">
                                <div class="video-thumbnail">
                                    <img class="size-medium" src="video-placeholder.png" alt="Video" />
                                    <div class="play-icon"></div>
                                </div>
                            </a>
                        </div>`;
                } else {
                    // 图片元素
                    customColumn.innerHTML = `
                        <div class="image-wrapper">
                            <a href="${url}" class="data-fancy" data-fancybox="gallery">
                                <img class="size-medium" src="${url}" alt="Image" />
                            </a>
                        </div>`;
                }
                
                fragment.appendChild(customColumn);
                this.loadedImages.add(url);
            }
        });
        
        console.log(`[displayImages] Adding ${newImagesAddedCount} new media elements to DOM fragment.`);
        gallery.appendChild(fragment);
        
        if (newImagesAddedCount > 0 || !this.msnry) { // Re-init if new images OR if masonry isn't there yet
            // 保存当前滚动位置
            const scrollPosition = window.scrollY;
            
            imagesLoaded(gallery, () => { 
                console.log('[displayImages] imagesLoaded callback. Re-initializing Masonry.');
                if (this.msnry) { 
                    try { this.msnry.destroy(); console.log("[displayImages] Old Masonry instance destroyed."); } 
                    catch(e) {console.warn("[displayImages] Error destroying old Masonry instance:", e)}
                }
                try {
                    this.msnry = new Masonry(gallery, {
                        itemSelector: '.custom-column',
                        columnWidth: 0, 
                        percentPosition: true,
                        transitionDuration: 0 // 禁用过渡动画
                    });
                    console.log('[displayImages] Masonry re-initialized successfully.');
                    
                    // 恢复滚动位置
                    window.scrollTo(0, scrollPosition);
                } catch(e) {
                    console.error('[displayImages] ERROR initializing Masonry:', e);
                }
            });
        } else if (this.msnry) {
             console.log('[displayImages] No new images, but ensuring Masonry layout is updated for existing items if any.');
             this.msnry.layout(); // Or this.msnry.reloadItems(); this.msnry.layout();
        }
    }
    
    async loadMoreImg() { 
        if (this.isLoading || this.noMoreData || (this.selectedSubfolder && this.selectedSubfolder !== 'all')) {
            this.toggleLoadMoreButton(false); 
            return;
        }
        const allPathSegments = this.folderList.map(f => f.pathSegment);
        const nextPathSegments = allPathSegments.slice(this.currentIndex, this.currentIndex + this.foldersPerLoad);

        if (nextPathSegments.length > 0) {
            await this.loadAndDisplayImages(nextPathSegments); 
            this.currentIndex += nextPathSegments.length;
            if (this.currentIndex >= allPathSegments.length) this.noMoreData = true;
        } else { this.noMoreData = true; }
        this.toggleLoadMoreButton(!this.noMoreData);
    }

    setupLoadMoreButton() { 
        const showObserver = !this.noMoreData && !(this.selectedSubfolder && this.selectedSubfolder !== 'all');
        // console.log(`[setupLoadMoreButton] noMoreData=${this.noMoreData}, specificSubfolderSelected=${!!(this.selectedSubfolder && this.selectedSubfolder !== 'all')}. Observer effective: ${showObserver}`);
        this.toggleLoadMoreButton(showObserver); // Also toggle physical button if its style is linked

        if (this.loadMoreObserver) { 
            this.loadMoreObserver.disconnect();
            if (this.observerElement && this.observerElement.parentNode) {
                this.observerElement.parentNode.removeChild(this.observerElement);
            }
            this.loadMoreObserver = null; 
            this.observerElement = null;  
        }

        if (!showObserver) { // If no more data or specific subfolder, don't setup new observer
             // console.log('[setupLoadMoreButton] Observer not needed or not effective, skipping setup.');
            return;
        }

        this.observerElement = document.createElement('div');
        this.observerElement.style.height = '10px'; 
        const galleryContainer = document.getElementById('imageGallery'); 
        if(galleryContainer && galleryContainer.parentNode) {
             galleryContainer.parentNode.insertBefore(this.observerElement, galleryContainer.nextSibling);
        } else { document.body.appendChild(this.observerElement); }

        this.loadMoreObserver = new IntersectionObserver(
            async (entries) => { 
                if (entries[0] && entries[0].isIntersecting) {
                    // console.log('[IntersectionObserver CALLBACK] Element intersecting, calling loadMoreImg.');
                    await this.loadMoreImg();
                }
            }, { root: null, rootMargin: '300px', threshold: 0.01 } 
        );
        this.loadMoreObserver.observe(this.observerElement);
        // console.log('[setupLoadMoreButton] Observer set up and observing.');
    }
    
    setupColumnSelector() { 
        console.log('[setupColumnSelector] Setting up column selector event listeners.');
        const buttons = document.querySelectorAll('.column-selector button');
        buttons.forEach(button => {
            // A simple way to ensure a listener isn't added multiple times if this method were re-called
            // (though in the current design, it's called once in initialize)
            if (button.hasAttribute('data-listener-attached')) return;

            button.addEventListener('click', () => { 
                const numColumns = button.getAttribute('data-columns');
                console.log(`[setupColumnSelector] Column button clicked: ${numColumns} columns`);
                
                buttons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                
                this.updateColumnLayout(parseInt(numColumns));
            });
            button.setAttribute('data-listener-attached', 'true');
        });
        console.log('[setupColumnSelector] Event listeners attached.');
    }

    updateColumnLayout(columns) {
        console.log(`[updateColumnLayout] Updating to ${columns} columns.`);
        this.currentColumns = parseInt(columns); // Ensure it's an integer
        
        const items = document.querySelectorAll('#imageRow .custom-column');
        
        let columnClassNumber;
        switch (this.currentColumns) {
            case 1: columnClassNumber = 12; break;
            case 2: columnClassNumber = 6; break;
            case 3: columnClassNumber = 4; break;
            case 4: columnClassNumber = 3; break;
            default: 
                console.warn(`[updateColumnLayout] Unsupported column count: ${this.currentColumns}. Defaulting to 3 columns (class number 4).`);
                columnClassNumber = 4; // Default to 3 columns (Bootstrap class col-*-4)
                this.currentColumns = 3; // Also update the state to reflect the fallback
        }
        console.log(`[updateColumnLayout] Calculated Bootstrap column class number: col-*-${columnClassNumber}`);

        items.forEach(item => {
            // Retain 'custom-column'. 'col-12' acts as a base for extra-small screens or as a reset.
            item.className = `custom-column col-12 col-sm-${columnClassNumber} col-md-${columnClassNumber} col-xl-${columnClassNumber}`;
        });
        
        const gallery = document.querySelector('#imageRow');
        if (gallery && gallery.childElementCount > 0) {
            imagesLoaded(gallery, () => { 
                console.log('[updateColumnLayout] imagesLoaded callback for column change. Re-initializing Masonry.');
                if (this.msnry) { 
                    try { this.msnry.destroy(); console.log("[updateColumnLayout] Old Masonry instance destroyed."); } 
                    catch(e) { console.warn("[updateColumnLayout] Error destroying old Masonry instance:", e); }
                }
                try {
                    this.msnry = new Masonry(gallery, {
                        itemSelector: '.custom-column',
                        columnWidth: 0, // Tells Masonry to use the width of the first itemSelector item
                        percentPosition: true // Recommended for fluid, percentage-based layouts
                    });
                    console.log('[updateColumnLayout] Masonry re-initialized successfully.');
                } catch(e) {
                    console.error('[updateColumnLayout] ERROR initializing Masonry:', e);
                }
            });
        } else {
            console.log('[updateColumnLayout] No items in gallery or gallery not found, Masonry not re-initialized.');
        }
    }

    async loadFavoritesView() {
        this.clearGalleryContent();
        
        if (this.favorites.length === 0) {
            this.showStatusMessage('No favorites yet.');
            return;
        }

        // Sort favorites by timestamp in descending order
        const sortedFavorites = [...this.favorites].sort((a, b) => 
            new Date(b.timestamp) - new Date(a.timestamp)
        );

        // Load images from all favorites
        for (const favorite of sortedFavorites) {
            const images = await this.loadImagesFromFavoriteFolder(favorite.month, favorite.subfolder);
            this.displayImages(images);
        }
    }

    async loadImagesFromFavoriteFolder(month, subfolderPathSegment) {
        console.log(`[loadImagesFromFavoriteFolder] START - Month: '${month}', Sub PathSegment: '${subfolderPathSegment}'`);
        if (!month || !subfolderPathSegment) { return []; }
        try {
            const encodedMain = encodeURIComponent(month);
            const pathForImageDirectory = FOLDER_BASE_PATH 
                ? `/${FOLDER_BASE_PATH}/${encodedMain}/${subfolderPathSegment}` 
                : `/${encodedMain}/${subfolderPathSegment}`;
            
            console.log(`[loadImagesFromFavoriteFolder] Fetching image list from URL: ${pathForImageDirectory}/`);
            const response = await fetch(pathForImageDirectory + '/'); 
            if (!response.ok) throw new Error(`HTTP error! Status: ${response.status} for ${pathForImageDirectory}/ (Code: ${response.status})`);
            const text = await response.text();
            const parser = new DOMParser();
            const doc = parser.parseFromString(text, 'text/html');
            const links = doc.querySelectorAll('a');
            
            const imageUrls = Array.from(links)
                .map(link => link.getAttribute('href'))
                .filter(href => {
                    if (!href || href === '../' || href.startsWith('?') || href.startsWith('/')) return false;
                    const decodedHrefForTest = this.decodeFolderName(href); 
                    return /\.(jpg|jpeg|png|gif|mp4|avi|webm|mov|mkv)$/i.test(decodedHrefForTest);
                }) 
                .map(href => {
                    let finalHrefSegment = href;
                    if (!href.includes('%') && /[^\w.\-\/]/.test(href)) {
                        finalHrefSegment = encodeURIComponent(href);
                    }
                    return `${pathForImageDirectory}/${finalHrefSegment}`;
                }); 
            console.log(`[loadImagesFromFavoriteFolder] END - Found ${imageUrls.length} image URLs for ${month}/${subfolderPathSegment}`);
            return imageUrls;
        } catch (error) {
            console.error(`[loadImagesFromFavoriteFolder] ERROR for Month: '${month}', SubPathSegment: '${subfolderPathSegment}'. Error:`, error);
            return [];
        }    
    }

    // Tags functionality
    async initializeTags() {
        this.viewTagsBtn = document.getElementById('viewTagsBtn');
        
        // Load initial tags
        await this.loadTags();
        
        // Setup event listeners
        this.tagsSelectorEl.addEventListener('change', async () => {
            if (!this.isViewingTags) return;  // 只在标签模式下响应
            
            if (!this.tagsSelectorEl.value) {
                this.taggedFoldersSelectorEl.innerHTML = '<option value="all">Select all tagged folders</option>';
                return;
            }
            await this.loadTaggedFolders(this.tagsSelectorEl.value);
        });

        // Tagged folders dropdown change event
        this.taggedFoldersSelectorEl.addEventListener('change', async (e) => {
            if (!this.isViewingTags) return;  // 只在标签模式下响应
            
            if (!e.target.value || e.target.value === 'all') {
                // 如果选择"all"，显示所有标签文件夹的图片
                await this.loadTagsView();
                return;
            }
            
            const {month, subfolder} = JSON.parse(e.target.value);
            // 清空当前内容
            this.clearGalleryContent();
            // 直接加载标签文件夹内容
            const images = await this.loadImagesFromTaggedFolder(month, subfolder);
            if (images.length > 0) {
                this.displayImages(images);
            } else {
                this.showStatusMessage('No images found in this tagged folder.');
            }
        });

        // View tags mode toggle
        this.viewTagsBtn.addEventListener('click', async () => {
            if (this.isViewingTags) {
                this.setViewMode('normal');
                // Reset to normal view
                if (this.selectedMainFolder) {
                    await this.reloadData();
                } else {
                    this.clearGalleryContent();
                    this.showStatusMessage('Please select a month.');
                }
            } else {
                this.setViewMode('tags');
                // Clear selections
                this.mainFolderSelectorEl.value = '';
                this.subFolderSelectorEl.value = 'all';
                this.favoritesSelectorEl.value = '';
                
                // Clear gallery content
                this.clearGalleryContent();
                this.showStatusMessage('Please select a tag to view images.');
            }
            this.updateURL();
        });
    }
    
    async loadTags() {
        try {
            const response = await fetch('tags.json');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            this.tags = data.tags || [];
            this.updateTagsDropdown();
        } catch (error) {
            console.error('Error loading tags:', error);
            this.tags = [];
        }
    }
    
    updateTagsDropdown() {
        const select = this.tagsSelectorEl;
        select.innerHTML = '<option value="">Select a tag</option>';
        
        this.tags.forEach(tag => {
            const option = document.createElement('option');
            option.value = tag.name;
            option.textContent = tag.name;
            select.appendChild(option);
        });
    }
    
    async loadTaggedFolders(tagName, afterRenderCallback) {
        try {
            const response = await fetch(`tags/${encodeURIComponent(tagName)}.json`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            const taggedFolders = data.folders || [];
            
            const select = this.taggedFoldersSelectorEl;
            select.innerHTML = '<option value="all">Select all tagged folders</option>';
            
            taggedFolders.forEach(folder => {
                const option = document.createElement('option');
                option.value = JSON.stringify({month: folder.month, subfolder: folder.subfolder});
                option.textContent = `${folder.displayName} (${folder.month})`;
                select.appendChild(option);
            });
            // 渲染完成后执行回调
            if (afterRenderCallback) afterRenderCallback();
        } catch (error) {
            console.error('Error loading tagged folders:', error);
            this.taggedFoldersSelectorEl.innerHTML = '<option value="all">Select all tagged folders</option>';
            if (afterRenderCallback) afterRenderCallback();
        }
    }
    
    async navigateToTaggedFolder(folder) {
        // 直接加载标签文件夹内容，不更新其他UI元素
        const images = await this.loadImagesFromTaggedFolder(folder.month, folder.subfolder);
        this.displayImages(images);
    }

    async loadTagsView() {
        this.clearGalleryContent();
        
        if (this.tags.length === 0) {
            this.showStatusMessage('No tags available.');
            return;
        }

        // Load images from all tagged folders
        for (const tag of this.tags) {
            try {
                const response = await fetch(`tags/${encodeURIComponent(tag.name)}.json`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                const taggedFolders = data.folders || [];
                
                for (const folder of taggedFolders) {
                    const images = await this.loadImagesFromTaggedFolder(folder.month, folder.subfolder);
                    this.displayImages(images);
                }
            } catch (error) {
                console.error(`Error loading tag ${tag.name}:`, error);
            }
        }
    }

    async loadImagesFromTaggedFolder(month, subfolderPathSegment) {
        console.log(`[loadImagesFromTaggedFolder] START - Month: '${month}', Sub PathSegment: '${subfolderPathSegment}'`);
        if (!month || !subfolderPathSegment) { return []; }
        try {
            const encodedMain = encodeURIComponent(month);
            const pathForImageDirectory = FOLDER_BASE_PATH 
                ? `/${FOLDER_BASE_PATH}/${encodedMain}/${subfolderPathSegment}` 
                : `/${encodedMain}/${subfolderPathSegment}`;
            
            console.log(`[loadImagesFromTaggedFolder] Fetching image list from URL: ${pathForImageDirectory}/`);
            const response = await fetch(pathForImageDirectory + '/'); 
            if (!response.ok) throw new Error(`HTTP error! Status: ${response.status} for ${pathForImageDirectory}/ (Code: ${response.status})`);
            const text = await response.text();
            const parser = new DOMParser();
            const doc = parser.parseFromString(text, 'text/html');
            const links = doc.querySelectorAll('a');
            
            const imageUrls = Array.from(links)
                .map(link => link.getAttribute('href'))
                .filter(href => {
                    if (!href || href === '../' || href.startsWith('?') || href.startsWith('/')) return false;
                    const decodedHrefForTest = this.decodeFolderName(href); 
                    return /\.(jpg|jpeg|png|gif|mp4|avi|webm|mov|mkv)$/i.test(decodedHrefForTest);
                }) 
                .map(href => {
                    // 使用原始href，不进行编码
                    return `${pathForImageDirectory}/${href}`;
                }); 
            console.log(`[loadImagesFromTaggedFolder] END - Found ${imageUrls.length} image URLs for ${month}/${subfolderPathSegment}`);
            return imageUrls;
        } catch (error) {
            console.error(`[loadImagesFromTaggedFolder] ERROR for Month: '${month}', SubPathSegment: '${subfolderPathSegment}'. Error:`, error);
            return [];
        }    
    }

    // 新增：设置视图模式的辅助方法
    setViewMode(mode) {
        // Cancel any ongoing requests when switching view modes
        this.cancelOngoingRequests();
        
        // 重置所有视图模式
        this.isViewingNormal = false;
        this.isViewingFavorites = false;
        this.isViewingTags = false;
        
        // 移除所有按钮的active类
        this.viewNormalBtn.classList.remove('active');
        this.viewFavoritesBtn.classList.remove('active');
        this.viewTagsBtn.classList.remove('active');
        
        // 设置新的视图模式
        switch(mode) {
            case 'normal':
                this.isViewingNormal = true;
                this.viewNormalBtn.classList.add('active');
                break;
            case 'favorites':
                this.isViewingFavorites = true;
                this.viewFavoritesBtn.classList.add('active');
                break;
            case 'tags':
                this.isViewingTags = true;
                this.viewTagsBtn.classList.add('active');
                break;
        }
    }

    // 新增：初始化普通视图模式
    async initializeNormalView() {
        this.viewNormalBtn.addEventListener('click', async () => {
            if (!this.isViewingNormal) {
                this.setViewMode('normal');
                if (this.selectedMainFolder) {
                    await this.reloadData();
                } else {
                    this.clearGalleryContent();
                    this.showStatusMessage('Please select a month.');
                }
                this.updateURL();
            }
        });
    }

    cancelOngoingRequests() {
        if (this.abortController) {
            console.log('[cancelOngoingRequests] Cancelling ongoing requests');
            this.abortController.abort();
            this.abortController = null;
        }
    }

    isVideoFile(url) {
        return /\.(mp4|avi|webm|mov|mkv)$/i.test(url);
    }
} // End of ImageGallery Class

// Initialize the gallery
var loadMoreCondition = (document.getElementsByClassName("load-more-container").length > 0) || (document.getElementsByClassName("load-more-rand-container").length > 0);

if (loadMoreCondition) { 
    console.log("Page condition met, initializing gallery system...");
    window.addEventListener('DOMContentLoaded', () => {
        console.log("DOMContentLoaded event fired. Initializing ImageGallery instance.");
        const galleryInstance = new ImageGallery();
        galleryInstance.initialize(); 

        // 返回顶部按钮功能
        const backToTopButton = document.getElementById('backToTop');
        
        // 初始化时检查滚动位置
        if (window.scrollY > 300) {
            backToTopButton.classList.add('show');
        }
        
        // 监听滚动事件
        window.addEventListener('scroll', () => {
            if (window.scrollY > 300) {
                backToTopButton.classList.add('show');
            } else {
                backToTopButton.classList.remove('show');
            }
        });

        // 点击事件处理
        backToTopButton.addEventListener('click', () => {
            // 滚动到图片区域顶部锚点，而不是页面顶部
            const galleryTopAnchor = document.getElementById('gallery-top-anchor');
            if (galleryTopAnchor) {
                galleryTopAnchor.scrollIntoView({
                    behavior: 'smooth'
                });
            } else {
                // 如果找不到锚点，回退到原来的行为
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }
        });
    });
} else {
    console.error("Page condition NOT met. Gallery system will NOT initialize.");
}