<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

$favoritesFile = __DIR__ . '/favorites.json';
$input = json_decode(file_get_contents('php://input'), true);

// Validate input
if (!isset($input['action']) || !isset($input['month']) || !isset($input['subfolder'])) {
    http_response_code(400);
    echo json_encode([
        'error' => 'Missing required parameters',
        'received' => $input
    ]);
    exit;
}

// Check directory permissions
if (!is_writable(__DIR__)) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Directory is not writable',
        'path' => __DIR__
    ]);
    exit;
}

// Read or create favorites file
if (file_exists($favoritesFile)) {
    if (!is_readable($favoritesFile)) {
        http_response_code(500);
        echo json_encode([
            'error' => 'File is not readable',
            'path' => $favoritesFile
        ]);
        exit;
    }
    $content = file_get_contents($favoritesFile);
    if ($content === false) {
        http_response_code(500);
        echo json_encode([
            'error' => 'Could not read file',
            'path' => $favoritesFile
        ]);
        exit;
    }
    $data = json_decode($content, true);
} else {
    $data = ['favorites' => []];
}

// Check if file is writable
if (!is_writable($favoritesFile) && file_exists($favoritesFile)) {
    http_response_code(500);
    echo json_encode([
        'error' => 'File is not writable',
        'path' => $favoritesFile
    ]);
    exit;
}

$id = $input['month'] . '_' . $input['subfolder'];

if ($input['action'] === 'add') {
    // Check if already exists
    $exists = false;
    foreach ($data['favorites'] as $favorite) {
        if ($favorite['id'] === $id) {
            $exists = true;
            break;
        }
    }
    
    if (!$exists) {
        $data['favorites'][] = [
            'id' => $id,
            'month' => $input['month'],
            'subfolder' => $input['subfolder'],
            'displayName' => $input['displayName'],
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
} elseif ($input['action'] === 'remove') {
    $data['favorites'] = array_filter($data['favorites'], function($item) use ($id) {
        return $item['id'] !== $id;
    });
    $data['favorites'] = array_values($data['favorites']); // Reset array keys
}

// Try to save the file
if (file_put_contents($favoritesFile, json_encode($data, JSON_PRETTY_PRINT)) === false) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Could not write to file',
        'path' => $favoritesFile
    ]);
    exit;
}

echo json_encode(['success' => true, 'data' => $data]);
?> 