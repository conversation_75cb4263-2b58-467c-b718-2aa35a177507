<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

$favoritesFile = __DIR__ . '/favorites.json';
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['favorites'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing favorites data']);
    exit;
}

if (!is_writable(__DIR__)) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Directory is not writable',
        'path' => __DIR__
    ]);
    exit;
}

// Try to save the file
if (file_put_contents($favoritesFile, json_encode($input, JSON_PRETTY_PRINT)) === false) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Could not write to file',
        'path' => $favoritesFile
    ]);
    exit;
}

echo json_encode(['success' => true]);
?> 